{"name": "galaxy", "version": "0.0.0", "license": "MIT", "scripts": {"start": "nx serve --project", "start:mobile-testing": "source ./tools/get-local-host-address.sh; nx serve --host=0.0.0.0 --public-host=$YOURHOSTNAME --project", "start:fast": "nx serve --configuration=fast --project", "start:fast-prod": "PROXY_ENV=prod npm run start:fast", "start:prod": "env PROXY_ENV=prod npm run start", "start:profile": "nx serve --configuration=profile --project", "build": "nx build", "bundle-analyzer": "nx run $PROJECT:build:production --sourceMap=true && npx source-map-explorer dist/apps/$PROJECT/**/*.*js{,.map}", "test": "nx test", "test:local": "nx test", "test:changes": "nx test --only-changed", "e2e": "nx e2e", "test:e2e": "nx run $npm_config_project-client-e2e:e2e --watch", "affected:apps": "nx affected:apps", "affected:libs": "nx affected:libs", "affected:print": "nx affected:apps; nx affected:libs", "affected:build": "nx affected:build", "affected:e2e": "nx affected:e2e", "affected:test": "nx affected:test", "affected:lint": "nx affected:lint", "affected:lint:styles": "node build/sass-lint-projects.js", "affected:dep-graph": "nx affected:dep-graph", "affected": "nx affected", "format": "nx format:write", "format:write": "nx format:write", "format:check": "nx format:check", "update": "nx migrate latest", "dep-graph": "nx dep-graph", "help": "nx help", "generate:new-general-page": "node ./tools/generators/generate.js --type=new-general-page --name=$npm_config_name", "generate:new-sandbox-page": "node ./tools/generators/generate.js --type=new-sandbox-page --name=$npm_config_name", "generate:new-observatory-doc-page": "node ./tools/generators/generate.js  --type=new-observatory-doc-page --name=$npm_config_name", "generate:new-galaxy-component": "node ./tools/generators/generate.js --type=new-galaxy-component --name=$npm_config_name && node ./tools/generators/generate.js --type=new-galaxy-component-doc-page --name=$npm_config_name", "generate:crm": "node ./tools/crm/generate.js", "extract-i18n": "node ./tools/extract-i18n/extract.ts", "extract-locl": "node ./tools/extract-locl/extract.ts", "update-angular-and-nx": "./tools/update-angular-and-nx/update-angular-and-nx.sh", "update-package-overrides-from-dependancies": "node ./tools/update-angular-and-nx/update-package-overrides.js", "update-webpack-module-federation-requiredversions": "node ./tools/update-angular-and-nx/update-webpack-module-federation-requiredversions.js", "workspace-generator": "nx workspace-generator", "postinstall": "husky install"}, "private": true, "dependencies": {"@angular-devkit/architect": "0.1901.4", "@angular/animations": "19.1.3", "@angular/cdk": "19.1.1", "@angular/common": "19.1.3", "@angular/compiler": "19.1.3", "@angular/core": "19.1.3", "@angular/elements": "19.1.3", "@angular/fire": "^18.0.1", "@angular/forms": "19.1.3", "@angular/google-maps": "19.1.1", "@angular/localize": "19.1.3", "@angular/material": "19.1.1", "@angular/material-date-fns-adapter": "19.1.1", "@angular/material-moment-adapter": "19.1.1", "@angular/platform-browser": "19.1.3", "@angular/platform-browser-dynamic": "19.1.3", "@angular/router": "19.1.3", "@angular/service-worker": "19.1.3", "@capacitor/android": "6.0.0", "@capacitor/app": "^6.0.0", "@capacitor/browser": "6.0.0", "@capacitor/core": "6.0.0", "@capacitor/device": "^6.0.0", "@capacitor/filesystem": "^6.0.0", "@capacitor/ios": "6.0.0", "@capacitor/keyboard": "^6.0.0", "@capacitor/network": "^6.0.0", "@capacitor/push-notifications": "^6.0.0", "@ckeditor/ckeditor5-angular": "^5.2.0", "@ckeditor/ckeditor5-build-classic": "^36.0.1", "@ckeditor/ckeditor5-core": "^36.0.1", "@ckeditor/ckeditor5-engine": "^36.0.1", "@ckeditor/ckeditor5-utils": "^36.0.1", "@ckeditor/ckeditor5-watchdog": "^36.0.1", "@ctrl/ngx-codemirror": "^5.1.1", "@ctrl/ngx-csv": "^6.0.0", "@ctrl/ngx-emoji-mart": "^9.2.0", "@dagrejs/graphlib": "^2.2.4", "@emotion/react": "^11.8.1", "@emotion/styled": "^11.8.1", "@googlemaps/js-api-loader": "^1.15.2", "@looker/embed-sdk": "^1.8.1", "@mui/icons-material": "^5.15.12", "@mui/lab": "^5.0.0-alpha.167", "@mui/material": "^5.15.12", "@mui/styles": "^5.15.12", "@ngx-translate/core": "14.0.0", "@nx/devkit": "20.3.2", "@peculiar/x509": "^1.9.5", "@rx-angular/state": "^1.6.1", "@storybook/types": "^8.2.9", "@swimlane/ngx-charts": "20.1.0", "@tinymce/tinymce-angular": "^6.0.1", "@udecode/plate": "16.5.0", "@udecode/plate-alignment": "16.5.0", "@udecode/plate-autoformat": "16.5.0", "@udecode/plate-basic-marks": "16.5.0", "@udecode/plate-block-quote": "16.5.0", "@udecode/plate-break": "16.5.0", "@udecode/plate-code-block": "16.5.0", "@udecode/plate-common": "7.0.2", "@udecode/plate-find-replace": "16.5.0", "@udecode/plate-heading": "16.5.0", "@udecode/plate-headless": "16.6.1", "@udecode/plate-highlight": "16.5.0", "@udecode/plate-indent-list": "16.5.0", "@udecode/plate-kbd": "16.5.0", "@udecode/plate-link": "16.5.0", "@udecode/plate-list": "16.5.0", "@udecode/plate-media": "16.5.0", "@udecode/plate-mention": "16.5.0", "@udecode/plate-node-id": "16.5.0", "@udecode/plate-normalizers": "16.5.0", "@udecode/plate-paragraph": "16.5.0", "@udecode/plate-reset-node": "16.5.0", "@udecode/plate-select": "16.5.0", "@udecode/plate-serializer-csv": "16.5.0", "@udecode/plate-serializer-md": "16.5.0", "@udecode/plate-styled-components": "16.5.0", "@udecode/plate-table": "16.5.0", "@udecode/plate-ui-alignment": "16.5.0", "@udecode/plate-ui-block-quote": "16.5.0", "@udecode/plate-ui-button": "16.5.0", "@udecode/plate-ui-code-block": "16.5.0", "@udecode/plate-ui-combobox": "16.5.0", "@udecode/plate-ui-cursor": "16.5.0", "@udecode/plate-ui-find-replace": "16.5.0", "@udecode/plate-ui-font": "16.5.0", "@udecode/plate-ui-line-height": "16.5.0", "@udecode/plate-ui-link": "16.5.0", "@udecode/plate-ui-list": "16.6.1", "@udecode/plate-ui-media": "16.5.0", "@udecode/plate-ui-mention": "16.5.0", "@udecode/plate-ui-placeholder": "16.5.0", "@udecode/plate-ui-table": "16.6.0", "@udecode/plate-ui-toolbar": "16.5.0", "@uiowa/digit-only": "^3.2.0", "@vendasta/account-group": "^0.34.0", "@vendasta/account-group-media": "^0.5.0", "@vendasta/accounts": "^0.8.3", "@vendasta/address": "^3.1.0", "@vendasta/advertising": "^7.13.9", "@vendasta/ai-assistants": "^0.43.0", "@vendasta/angular-social-mentions": "^1.0.6", "@vendasta/atlas": "^8.19.0", "@vendasta/automata": "^3.65.0", "@vendasta/billing": "^14.46.0", "@vendasta/bulk-actions": "^0.13.1", "@vendasta/business-center": "^0.27.0", "@vendasta/campaigns": "^3.64.0", "@vendasta/category": "^3.12.0", "@vendasta/composer": "^2.4.0", "@vendasta/configuration-management": "^1.0.3", "@vendasta/constant-contact": "^0.4.0", "@vendasta/contacts": "^3.1.0", "@vendasta/conversation": "^0.104.0", "@vendasta/crm": "^0.56.0", "@vendasta/crm-integrations": "^0.10.0", "@vendasta/customer-voice-service": "^0.13.0", "@vendasta/data-warehouse": "^0.20.0", "@vendasta/domain": "^0.4.0", "@vendasta/email": "^4.33.0", "@vendasta/email-builder": "0.0.21", "@vendasta/embeddings": "^0.28.0", "@vendasta/event-broker": "^0.5.0", "@vendasta/executive-report": "^3.4.0", "@vendasta/facebook": "^4.10.0", "@vendasta/forms_microservice": "^0.17.0", "@vendasta/g-suite": "^3.1.0", "@vendasta/godaddy": "^4.1.0", "@vendasta/google-analytics": "^3.4.0", "@vendasta/google-my-business": "^3.4.0", "@vendasta/google-search-console": "^0.3.0", "@vendasta/help-center": "^1.4.0", "@vendasta/iam": "^3.3.2", "@vendasta/iamv2": "^2.11.0", "@vendasta/instagram": "^3.3.0", "@vendasta/lexicon": "^5.1.0", "@vendasta/listing-products": "https://storage.googleapis.com/v-libraries/b448be2e-0871-40b4-9220-82e297e3c570/vendasta-listing-products-4.88.0.tgz", "@vendasta/listing-score": "^0.0.1", "@vendasta/listing-sync-pro": "^6.10.1", "@vendasta/listing-syndication": "^1.4.0", "@vendasta/local-marketing-index": "^0.5.0", "@vendasta/marketplace-analytics": "^1.13.0", "@vendasta/marketplace-apps": "^4.40.0", "@vendasta/marketplace-packages": "^56.4.1", "@vendasta/matchcraft": "^0.5.0", "@vendasta/media": "4.2.0", "@vendasta/meeting-analysis": "0.6.0", "@vendasta/meetings": "^0.98.4", "@vendasta/mission-control": "^3.6.0", "@vendasta/ms-office": "^4.1.0", "@vendasta/multi-location": "^3.3.0", "@vendasta/multi-location-analytics": "^4.1.0", "@vendasta/nap": "^0.2.0", "@vendasta/notifications-sdk": "^1.7.0", "@vendasta/order-fulfillment": "^6.19.0", "@vendasta/partner": "^1.47.0", "@vendasta/platform-users": "^0.11.0", "@vendasta/prospect": "^0.33.0", "@vendasta/quickbooks": "^3.1.0", "@vendasta/reputation": "^7.44.0", "@vendasta/sales": "^10.3.0", "@vendasta/sales-opportunities": "^6.21.0", "@vendasta/sales-orders": "^4.4.0", "@vendasta/sales-v2": "^3.1.2", "@vendasta/shoppable-feed": "^0.6.0", "@vendasta/sms": "^0.9.0", "@vendasta/smsv2": "^0.6.5", "@vendasta/snapshot": "^4.14.0", "@vendasta/snapshot-widget": "^0.9.0", "@vendasta/social-drafts": "^3.6.8", "@vendasta/social-posts": "^5.42.2", "@vendasta/spambam": "^0.0.6", "@vendasta/sre-reporting": "^3.8.0", "@vendasta/sso": "2.11.0", "@vendasta/support": "^0.2.0", "@vendasta/task": "^6.5.0", "@vendasta/task-manager": "^7.23.0", "@vendasta/telephony": "^0.7.0", "@vendasta/templates": "^4.10.0", "@vendasta/the-loop": "^0.17.0", "@vendasta/url-shortener": "^5.2.0", "@vendasta/vendasta-yesware-bridge": "^0.2.0", "@vendasta/voice": "^0.1.0", "@vendasta/web-crawler": "^3.16.0", "@vendasta/wordfence": "^1.2.1", "@vendasta/wsp-accounts": "^3.3.0", "@vendasta/wsp-logger": "^0.5.0", "@vendasta/wsp-monitor": "^0.9.4", "@vendasta/wsp-support-tools": "2.2.2", "@vendasta/wsp-wp-manager": "^2.53.0", "@vendasta/yext": "^0.5.0", "@vvo/tzdb": "^6.51.0", "angular-calendar": "^0.31.1", "angular-google-charts": "^2.2.3", "angular-oauth2-oidc": "^13.0.1", "angular-tag-cloud-module": "^14.0.0", "angular2-websocket": "^0.9.8", "apexcharts": "^4.3.0", "assert": "^2.0.0", "assert-never": "^1.2.1", "autolinker": "^4.0.0", "autonumeric": "^4.9.0", "autoprefixer": "^9.8.6", "brace": "^0.11.1", "calendar-utils": "^0.10.4", "capacitor-native-settings": "^6.0.1", "capacitor-secure-storage-plugin": "0.10.0", "chart.js": "^4.2.1", "chartjs-plugin-datalabels": "^2.2.0", "chromatic": "^11.0.0", "codemirror": "^5.60.0", "core-js": "3.36.1", "countries-and-timezones": "^3.5.1", "country-list": "^2.3.0", "cropperjs": "^1.5.12", "css-vars-ponyfill": "^2.4.6", "currency-symbol-map": "^5.1.0", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.1", "dayjs": "^1.11.10", "diff-match-patch-ts": "^0.6.0", "dom-autoscroller": "^2.3.4", "dompurify": "^2.2.9", "dragula": "^3.7.3", "fast-deep-equal": "^3.1.3", "file-saver": "^2.0.5", "firebase": "^10.12.2", "fuse.js": "^6.4.6", "globalthis": "^1.0.3", "google-libphonenumber": "^3.2.40", "hammerjs": "^2.0.8", "he": "^1.2.0", "highcharts": "^9.1.2", "highlight.js": "^11.7.0", "html-to-image": "^1.6.2", "immutable": "^3.8.2", "intl": "^1.2.5", "ionic-appauth": "^2.1.0", "jquery": "^3.6.0", "js-base64": "^3.7.2", "jsencrypt": "^3.2.1", "jsonpath": "^1.1.1", "jwt-decode": "^3.1.2", "latest": "^0.2.0", "libphonenumber-js": "^1.9.23", "locale-currency": "^0.0.2", "lodash-es": "^4.17.21", "markdown-it": "^12.0.6", "marked": "^12.0.2", "material-icons": "^1.13.11", "md5": "^2.3.0", "mime": "^4.0.1", "moment": "^2.29.1", "moment-timezone": "^0.5.33", "ng-apexcharts": "^1.15.0", "ng-click-outside": "^7.0.1", "ng-in-viewport": "^13.0.1", "ng-recaptcha": "^10.0.0", "ng2-charts": "^4.1.1", "ng2-dragula": "^3.2.0", "ng2-google-charts": "^7.0.0", "ngx-ace-wrapper": "^14.0.0", "ngx-color-picker": "^14.0.0", "ngx-cookie-service": "^15.0.0", "ngx-csv-parser": "^2.0.0", "ngx-diff": "^9.0.0", "ngx-image-cropper": "^6.3.1", "ngx-infinite-scroll": "^15.0.0", "ngx-json-viewer": "^3.2.1", "ngx-markdown": "^17.2.1", "ngx-mat-select-search": "^7.0.2", "ngx-owl-carousel-o": "^16.0.0", "ngx-pagination": "^6.0.3", "ngx-papaparse": "^7.0.0", "ngx-skeleton-loader": "^8.1.0", "ngx-webstorage": "^11.1.1", "npm": "^10.9.2", "posthog-js": "^1.146.0", "pretty-ms": "^7.0.1", "product-fruits": "^1.0.24", "psl": "^1.9.0", "react": "^17.0.2", "react-beautiful-dnd": "^13.1.0", "react-color": "^2.19.3", "react-dom": "^17.0.2", "react-icons": "^4.2.0", "react-intersection-observer": "^8.32.0", "react-virtualized-auto-sizer": "^1.0.5", "react-window": "^1.8.6", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "rxjs-compat": "^6.5.5", "showdown": "^1.9.1", "slate": "^0.81.1", "slate-history": "^0.66.0", "slate-hyperscript": "^0.77.0", "slate-react": "^0.81.0", "slugify": "^1.6.6", "sql-formatter": "^15.0.2", "srcdoc-polyfill": "^1.0.0", "string-argv": "^0.3.2", "styled-components": "^5.2.1", "stylelint-declaration-strict-value": "^1.9.2", "swiper": "9.3.0", "tdigest": "^0.1.1", "tinymce": "^5.10.6", "ts-md5": "^1.2.9", "twitter-text": "^2.0.5", "uuid": "^8.3.2", "zone.js": "0.15.0"}, "devDependencies": {"@4tw/cypress-drag-drop": "^2.2.3", "@angular-builders/custom-esbuild": "18.0.0", "@angular-builders/custom-webpack": "19.0.0", "@angular-devkit/build-angular": "19.1.4", "@angular-devkit/core": "19.1.4", "@angular-devkit/schematics": "19.1.4", "@angular-eslint/eslint-plugin": "19.0.2", "@angular-eslint/eslint-plugin-template": "19.0.2", "@angular-eslint/template-parser": "19.0.2", "@angular/cli": "19.1.4", "@angular/compiler-cli": "19.1.3", "@angular/language-service": "19.1.3", "@capacitor/assets": "^3.0.0", "@capacitor/cli": "6.0.0", "@chromatic-com/storybook": "^3.2.2", "@google-cloud/storage": "^5.18.0", "@locl/cli": "^1.0.0", "@ngneat/spectator": "19.2.0", "@nx/angular": "20.3.2", "@nx/cypress": "20.3.2", "@nx/eslint": "20.3.2", "@nx/eslint-plugin": "20.3.2", "@nx/jest": "20.3.2", "@nx/js": "20.3.2", "@nx/plugin": "20.3.2", "@nx/storybook": "20.3.2", "@nx/web": "20.3.2", "@nx/workspace": "20.3.2", "@schematics/angular": "19.1.4", "@storybook/addon-essentials": "8.5.0", "@storybook/addon-interactions": "8.5.0", "@storybook/addon-links": "8.5.0", "@storybook/angular": "8.5.0", "@storybook/core-common": "8.5.0", "@storybook/core-server": "8.5.0", "@storybook/test": "^8.4.7", "@storybook/test-runner": "0.21.0", "@storybook/testing-library": "^0.2.2", "@swc-node/register": "1.9.2", "@swc/cli": "0.5.2", "@swc/core": "1.5.7", "@testing-library/jest-dom": "^5.16.2", "@types/core-js": "^2.5.4", "@types/country-list": "^2.1.0", "@types/dompurify": "^2.2.3", "@types/file-saver": "^2.0.3", "@types/google-libphonenumber": "^7.4.21", "@types/google.maps": "^3.58.1", "@types/hammerjs": "^2.0.40", "@types/highcharts": "^5.0.44", "@types/jest": "29.5.14", "@types/jwt-decode": "^2.2.1", "@types/knockout": "^3.4.71", "@types/lodash": "^4.14.172", "@types/lodash-es": "^4.17.5", "@types/luxon": "^3.6.2", "@types/md5": "^2.3.2", "@types/node": "^18.16.9", "@types/papaparse": "^5.3.7", "@types/react": "17.0.19", "@types/react-beautiful-dnd": "^13.1.2", "@types/react-color": "^3.0.4", "@types/react-dom": "17.0.19", "@types/react-virtualized-auto-sizer": "^1.0.0", "@types/react-window": "^1.8.5", "@types/stripe-v3": "^3.1.25", "@types/styled-components": "^5.1.25", "@types/testing-library__jest-dom": "^5.14.2", "@types/uuid": "^8.3.1", "@types/webpack-env": "^1.16.0", "@typescript-eslint/eslint-plugin": "7.16.1", "@typescript-eslint/parser": "7.16.1", "@typescript-eslint/utils": "7.16.1", "@vendasta/api-gateway": "1.3.0", "@vendasta/platform-integrations": "^1.24.0", "angular-http-server": "^1.10.0", "babel-preset-env": "^1.7.0", "chokidar": "^4.0.2", "cross-env": "7.0.3", "cypress": "13.13.1", "cypress-file-upload": "^5.0.8", "cypress-real-events": "^1.7.6", "cypress-wait-until": "^1.7.2", "dotenv": "^8.2.0", "eslint": "8.57.0", "eslint-config-prettier": "9.0.0", "eslint-plugin-cypress": "^2.13.4", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-react-hooks": "^4.3.0", "eslint-plugin-storybook": "0.11.2", "html-webpack-plugin": "^5.5.0", "http-browserify": "^1.7.0", "https-browserify": "^1.0.0", "husky": "^8.0.3", "inquirer": "^7.3.3", "inquirer-autocomplete-prompt": "^1.3.0", "jest": "29.7.0", "jest-canvas-mock": "^2.3.1", "jest-environment-jsdom": "29.7.0", "jest-localstorage-mock": "^2.4.14", "jest-preset-angular": "14.4.2", "jest-zone-patch": "^0.0.10", "js-beautify": "^1.13.13", "jsdom": "^19.0.0", "jsonc-eslint-parser": "^2.1.0", "lodash": "^4.17.21", "mockdate": "^3.0.5", "moment-locales-webpack-plugin": "^1.2.0", "ng-mocks": "^14.13.0", "ng-packagr": "19.1.0", "ngx-highlightjs": "^8.0.0", "ngx-translate-testing": "6.1.0", "npm-check-updates": "^16.10.8", "nx": "20.3.2", "postcss": "^8.3.9", "postcss-import": "14.1.0", "postcss-preset-env": "7.5.0", "postcss-url": "10.1.3", "prettier": "3.3.3", "pretty-quick": "^4.0.0", "resize-observer-polyfill": "^1.5.1", "sass-loader": "^10.1.1", "serve": "^12.0.0", "speed-measure-webpack5-plugin": "^1.3.3", "storybook": "8.5.0", "stream-browserify": "^3.0.0", "stylelint": "^13.13.1", "ts-jest": "29.1.0", "ts-mockito": "^2.6.1", "ts-morph": "^15.1.0", "ts-node": "10.9.1", "ts-prune": "^0.10.3", "tslib": "^2.5.0", "typescript": "5.6.3", "typescript-plugin-css-modules": "^5.1.0", "typings-for-css-modules-loader": "^1.7.0", "webpack": "^5.64.0"}, "overrides": {"@types/countries-and-timezones": "../_EXCLUDED_", "@angular-devkit/build-angular": "19.1.4", "@angular-devkit/core": "19.1.4", "@angular-devkit/architect": "0.1901.4", "@angular/animations": "19.1.3", "@angular/compiler-cli": "19.1.3", "@angular/common": "19.1.3", "@angular/core": "19.1.3", "@angular/cdk": "19.1.1", "@angular/compiler": "19.1.3", "@angular/forms": "19.1.3", "@angular/localize": "19.1.3", "@angular/material": "19.1.1", "@angular/platform-browser": "19.1.3", "@angular/platform-browser-dynamic": "19.1.3", "@angular/router": "19.1.3", "@ngx-translate/core": "14.0.0", "chokidar": "^4.0.2", "core-js": "3.36.1", "cypress": "13.13.1", "rxjs": "^7.8.0", "typescript": "5.6.3", "webpack": "^5.64.0", "zone.js": "0.15.0"}, "browser": {"fs": false, "path": false, "os": false}, "engines": {"node": ">=20.10.0 <21.0.0", "npm": ">=10.2.0 <12.0.0"}, "sideEffects": ["polyfill.base.ts", "apps/**/src/polyfills.ts"], "resolutions": {"@types/react": "17.0.19", "@types/react-dom": "17.0.19"}}