import { Injectable, inject } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import {
  AccountGroupApiService,
  GetMultiRequest,
  ProjectionFilter as AGProjectionFilter,
} from '@vendasta/account-group';
import { ListingProfileApiService } from '@vendasta/listing-products';
import { BrandsService } from '../../brands.service';
import { BrandContext, QueryService } from '../../../metrics/query.service';
import { TranslateService } from '@ngx-translate/core';
import { BulkKeywordService, BusinessKeywordData } from './bulk-keyword.service';
import {
  MultiLocationAnalyticsService,
  QueryMetricsRequest,
  QueryMetricsResponse,
  ResourceId,
  GroupResourceId,
  GroupBy,
  AlignmentPeriod,
  AlignmentPeriodCalendar,
  Alignment,
  Order,
  MetricResult,
} from '@vendasta/multi-location-analytics';
import dayjs from 'dayjs';
import { partnerId } from '../../../../globals';
import { ConsolidatedDataService } from './consolidated-data.service';

export interface LocationDisplay {
  accountGroupId: string;
  name: string;
  address: string;
  isPro: boolean;
  customerIdentifier: string;
  tags: string[];
  keywordCount: number;
  keywordLimit: number;
  keywordUsage: string; // e.g., "5/15"
  usagePercentage: number; // 0-100
  canAddKeywords: boolean;
  isNearLimit: boolean; // >80% usage
  isAtLimit: boolean; // 100% usage
  statusTag: 'Low Usage' | 'High Usage' | 'Near Limit' | 'At Limit' | 'No Keywords' | null;
}

@Injectable({
  providedIn: 'root',
})
export class BulkLocationService {
  private accountGroupService = inject(AccountGroupApiService);
  private listingProfileService = inject(ListingProfileApiService);
  private brandsService = inject(BrandsService);
  private queryService = inject(QueryService);
  private translate = inject(TranslateService);
  private bulkKeywordService = inject(BulkKeywordService);
  private mla = inject(MultiLocationAnalyticsService);
  private consolidatedDataService = inject(ConsolidatedDataService);

  /**
   * Get available locations for keyword assignment from current brand context
   * Now uses consolidated data API for improved performance
   */
  async getAvailableLocations(): Promise<LocationDisplay[]> {
    try {
      // Get current brand context
      const brandContext = await firstValueFrom(this.queryService.brandsContext$);

      if (!brandContext || !brandContext.resourceIds?.length) {
        console.warn('No brand context available');
        return [];
      }

      // Extract business IDs from the brand context
      const businessIds = await this.extractBusinessIdsFromContext(brandContext);

      if (businessIds.length === 0) {
        console.warn('No business IDs found in brand context - using alternative method');
        // Fall back to getting business IDs through the brand service
        // This is a placeholder - in practice, you would integrate with the existing
        // brand keyword tracking service to get the business IDs
        return [];
      }

      // Use consolidated data API instead of multiple separate calls
      const consolidatedResponse = await this.consolidatedDataService.getConsolidatedData(businessIds);
      const businessDataMap = this.consolidatedDataService.extractBusinessKeywordDataFromConsolidated(consolidatedResponse);

      // Convert to LocationDisplay format
      const locations: LocationDisplay[] = [];

      for (const [businessId, businessData] of businessDataMap) {
        const usagePercentage =
          businessData.keywordLimit > 0 ? (businessData.existingKeywords.length / businessData.keywordLimit) * 100 : 0;
        const isAtLimit = businessData.existingKeywords.length >= businessData.keywordLimit;
        const isNearLimit = usagePercentage >= 80 && !isAtLimit;

        const location: LocationDisplay = {
          accountGroupId: businessId,
          name: businessData.businessName,
          address: businessData.address,
          isPro: businessData.isPro,
          customerIdentifier: businessId,
          tags: this.generateTagsFromBusinessData(businessData),
          keywordCount: businessData.existingKeywords.length,
          keywordLimit: businessData.keywordLimit,
          keywordUsage: `${businessData.existingKeywords.length}/${businessData.keywordLimit}`,
          usagePercentage: Math.round(usagePercentage),
          canAddKeywords: !isAtLimit,
          isNearLimit,
          isAtLimit,
          statusTag: this.getStatusTag(businessData),
        };

        locations.push(location);
      }

      // Sort by name for better UX
      return locations.sort((a, b) => a.name.localeCompare(b.name));
    } catch (error) {
      console.error('Failed to get available locations:', error);
      return [];
    }
  }

  /**
   * Get locations with detailed keyword information for selection
   * Now uses consolidated data API for improved performance
   */
  async getLocationsWithKeywordData(businessIds?: string[]): Promise<LocationDisplay[]> {
    try {
      const targetBusinessIds = businessIds;

      // If no specific business IDs provided, get all from brand context
      if (!targetBusinessIds) {
        const locations = await this.getAvailableLocations();
        return locations;
      }

      // Use consolidated data API instead of multiple separate calls
      const consolidatedResponse = await this.consolidatedDataService.getConsolidatedData(targetBusinessIds);
      const businessDataMap = this.consolidatedDataService.extractBusinessKeywordDataFromConsolidated(consolidatedResponse);

      const locations: LocationDisplay[] = [];

      for (const [businessId, businessData] of businessDataMap) {
        const usagePercentage =
          businessData.keywordLimit > 0 ? (businessData.existingKeywords.length / businessData.keywordLimit) * 100 : 0;
        const isAtLimit = businessData.existingKeywords.length >= businessData.keywordLimit;
        const isNearLimit = usagePercentage >= 80 && !isAtLimit;

        const location: LocationDisplay = {
          accountGroupId: businessId,
          name: businessData.businessName,
          address: businessData.address,
          isPro: businessData.isPro,
          customerIdentifier: businessId,
          tags: this.generateTagsFromBusinessData(businessData),
          keywordCount: businessData.existingKeywords.length,
          keywordLimit: businessData.keywordLimit,
          keywordUsage: `${businessData.existingKeywords.length}/${businessData.keywordLimit}`,
          usagePercentage: Math.round(usagePercentage),
          canAddKeywords: !isAtLimit,
          isNearLimit,
          isAtLimit,
          statusTag: this.getStatusTag(businessData),
        };

        locations.push(location);
      }

      return locations.sort((a, b) => a.name.localeCompare(b.name));
    } catch (error) {
      console.error('Failed to get locations with keyword data:', error);
      return [];
    }
  }

  /**
   * Check if locations can accept new keywords with detailed validation
   * Now uses consolidated data API for improved performance
   */
  async validateKeywordCapacity(
    businessIds: string[],
    keywordCount: number,
  ): Promise<{
    canAddToAll: boolean;
    capacityByBusiness: Map<string, { canAdd: number; willSkip: number; atLimit: boolean }>;
    validationSummary: {
      totalLocations: number;
      locationsAtLimit: number;
      locationsNearLimit: number;
      totalKeywordsCanAdd: number;
      totalKeywordsWillSkip: number;
    };
  }> {
    try {
      // Use consolidated data API instead of multiple separate calls
      const consolidatedResponse = await this.consolidatedDataService.getConsolidatedData(businessIds);
      const businessDataMap = this.consolidatedDataService.extractBusinessKeywordDataFromConsolidated(consolidatedResponse);
      
      const capacityByBusiness = new Map<string, { canAdd: number; willSkip: number; atLimit: boolean }>();
      let canAddToAll = true;
      let locationsAtLimit = 0;
      let locationsNearLimit = 0;
      let totalKeywordsCanAdd = 0;
      let totalKeywordsWillSkip = 0;

      for (const [businessId, businessData] of businessDataMap) {
        const availableSlots = businessData.keywordLimit - businessData.existingKeywords.length;
        const canAdd = Math.min(keywordCount, availableSlots);
        const willSkip = keywordCount - canAdd;
        const atLimit = availableSlots <= 0;
        const usagePercentage =
          businessData.keywordLimit > 0 ? (businessData.existingKeywords.length / businessData.keywordLimit) * 100 : 0;

        capacityByBusiness.set(businessId, {
          canAdd,
          willSkip,
          atLimit,
        });

        // Update counters
        totalKeywordsCanAdd += canAdd;
        totalKeywordsWillSkip += willSkip;

        if (atLimit) {
          canAddToAll = false;
          locationsAtLimit++;
        } else if (usagePercentage >= 80) {
          locationsNearLimit++;
        }
      }

      return {
        canAddToAll,
        capacityByBusiness,
        validationSummary: {
          totalLocations: businessIds.length,
          locationsAtLimit,
          locationsNearLimit,
          totalKeywordsCanAdd,
          totalKeywordsWillSkip,
        },
      };
    } catch (error) {
      console.error('Failed to validate keyword capacity:', error);
      return {
        canAddToAll: false,
        capacityByBusiness: new Map(),
        validationSummary: {
          totalLocations: 0,
          locationsAtLimit: 0,
          locationsNearLimit: 0,
          totalKeywordsCanAdd: 0,
          totalKeywordsWillSkip: 0,
        },
      };
    }
  }

  /**
   * Get partner and market information for keyword operations
   * Now uses consolidated data API for improved performance
   */
  async getPartnerMarketInfo(): Promise<{ partnerId?: string; marketId?: string }> {
    try {
      const brandContext = await firstValueFrom(this.queryService.brandsContext$);

      if (!brandContext || !brandContext.resourceIds?.length) {
        return {};
      }

      // Try to get partner/market info from the first business
      const businessIds = await this.extractBusinessIdsFromContext(brandContext);

      if (businessIds.length === 0) {
        return {};
      }

      // Use consolidated data API to get partner/market info
      const consolidatedResponse = await this.consolidatedDataService.getConsolidatedData([businessIds[0]]);
      const businessDataMap = this.consolidatedDataService.extractBusinessKeywordDataFromConsolidated(consolidatedResponse);
      
      const firstBusinessData = businessDataMap.get(businessIds[0]);

      return {
        partnerId: firstBusinessData?.partnerId,
        marketId: firstBusinessData?.marketId,
      };
    } catch (error) {
      console.error('Failed to get partner/market info:', error);
      return {};
    }
  }

  /**
   * Extract business IDs from brand context
   * Uses the same pattern as BrandKeywordTrackingService to get business IDs from analytics
   */
  private async extractBusinessIdsFromContext(brandContext: BrandContext): Promise<string[]> {
    if (!brandContext.resourceIds?.length) {
      return [];
    }

    // Get the group ID from the brand context
    const groupId = brandContext.resourceIds[0]?.groupId?.groupPathNodes?.[0];

    if (!groupId) {
      return [];
    }

    try {
      // Use the same pattern as BrandKeywordTrackingService to get business IDs
      const endDate = new Date();
      const request = this.generateFavoriteKeywordsQueryMetricsRequest(groupId, endDate);

      const response = await firstValueFrom(this.mla.queryMetrics(request));
      const businessIds = this.parseBusinessIdsFromQueryMetricsResult(response);

      return businessIds;
    } catch (error) {
      console.error('Failed to extract business IDs from brand context:', error);
      return [];
    }
  }

  /**
   * Generate the same query request used by BrandKeywordTrackingService to get business IDs
   */
  private generateFavoriteKeywordsQueryMetricsRequest(groupId: string, endDate: Date): QueryMetricsRequest {
    const startDate = dayjs(endDate).subtract(12, 'months').toDate();
    return new QueryMetricsRequest({
      partnerId: partnerId, // Use the same partnerId from globals
      metricName: 'seo_settings',
      resourceIds: [
        new ResourceId({
          groupId: new GroupResourceId({ groupPathNodes: [groupId] }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [
          { dimension: 'business_id' },
          { limitDimension: { dimension: 'updated', order: Order.ORDER_DESC, limit: 1 } },
          { dimension: 'favorite_keywords' },
        ],
      }),
      alignmentPeriod: new AlignmentPeriod({
        calendar: AlignmentPeriodCalendar.CALENDAR_MINUTE,
      }),
      alignment: Alignment.ALIGN_DELTA,
      dateRange: {
        start: startDate,
        end: this.addDays(endDate, 10),
      },
    });
  }

  /**
   * Parse business IDs from the query metrics response
   * Following the same pattern as parseFavoriteKeywordsFromQueryMetricsResult
   */
  private parseBusinessIdsFromQueryMetricsResult(response: QueryMetricsResponse): string[] {
    const businessIds = new Set<string>();

    // Use unwrapMetricsResponse to get the MetricResult[] format
    const unwrappedResults = this.mla.unwrapMetricsResponse(response);

    unwrappedResults?.forEach((metric: MetricResult) => {
      const businessId = metric.dimension;
      if (businessId) {
        businessIds.add(businessId);
      }
    });

    return Array.from(businessIds);
  }

  /**
   * Add days to a date (helper function)
   */
  private addDays(date: Date, days: number): Date {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  }

  /**
   * Generate descriptive tags for a business based on its data
   */
  private generateTagsFromBusinessData(businessData: BusinessKeywordData): string[] {
    const tags: string[] = [];

    // Edition tag
    tags.push(businessData.isPro ? 'Pro' : 'Standard');

    // Add status tag if applicable
    const statusTag = this.getStatusTag(businessData);
    if (statusTag) {
      tags.push(statusTag);
    }

    return tags;
  }

  /**
   * Get status tag based on keyword usage
   */
  private getStatusTag(
    businessData: BusinessKeywordData,
  ): 'Low Usage' | 'High Usage' | 'Near Limit' | 'At Limit' | 'No Keywords' | null {
    const usagePercentage =
      businessData.keywordLimit > 0 ? (businessData.existingKeywords.length / businessData.keywordLimit) * 100 : 0;

    if (businessData.existingKeywords.length === 0) {
      return 'No Keywords';
    } else if (usagePercentage >= 100) {
      return 'At Limit';
    } else if (usagePercentage >= 80) {
      return 'Near Limit';
    } else if (usagePercentage >= 70) {
      return 'High Usage';
    } else if (usagePercentage <= 25) {
      return 'Low Usage';
    }

    return null;
  }

  /**
   * Format location for display in selection components
   */
  formatLocationForDisplay(location: LocationDisplay): string {
    return `${location.name} (${location.keywordUsage})`;
  }

  /**
   * Get location summary for bulk operations
   */
  getLocationSummary(locations: LocationDisplay[]): {
    total: number;
    proCount: number;
    standardCount: number;
    averageUsage: number;
    atLimitCount: number;
  } {
    const total = locations.length;
    const proCount = locations.filter((l) => l.isPro).length;
    const standardCount = total - proCount;
    const atLimitCount = locations.filter((l) => l.keywordCount >= l.keywordLimit).length;

    const totalUsage = locations.reduce((sum, l) => {
      return sum + (l.keywordLimit > 0 ? l.keywordCount / l.keywordLimit : 0);
    }, 0);

    const averageUsage = total > 0 ? (totalUsage / total) * 100 : 0;

    return {
      total,
      proCount,
      standardCount,
      averageUsage: Math.round(averageUsage),
      atLimitCount,
    };
  }
}
