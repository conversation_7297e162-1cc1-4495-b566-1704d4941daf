import { Injectable, inject } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { 
  ListingProfileApiService,
  GetConsolidatedListingProfileDataRequest,
  GetConsolidatedListingProfileDataResponse,
  ConsolidatedDataContainer,
  ProjectionFilter
} from '@vendasta/listing-products';

export interface ConsolidatedDataRequest {
  businessIds: string[];
  projectionFilter: {
    externalIdentifiers: boolean;
    socialUrls: boolean;
    richData: boolean;
    napData: boolean;
    businessHours: boolean;
    googleAttributes: boolean;
    googleAttributesMetadata: boolean;
    bingAttributes: boolean;
    bingAttributesMetadata: boolean;
    legacyProductDetails: boolean;
  };
  readFilter: {
    includeDeleted: boolean;
  };
  languageCode: string;
}

export interface ConsolidatedDataItem {
  businessId: string;
  listingProfile: {
    businessId: string;
    created: string;
    updated: string;
    externalIdentifiers: {
      partnerId: string;
      taxIds: string[];
      vCategoryIds: string[];
      marketId: string;
      salesPersonId: string;
      tags: string[];
      customerIdentifier: string;
      origin: string;
      socialProfileId: string;
      updateOrigin: string;
    };
    socialUrls: Record<string, any>;
    richData: {
      paymentMethods: string[];
      serviceAvailability: Record<string, any>;
      cellNumber: string;
      seoKeywords: string[];
      syncingSeoKeywords: string[];
    };
    napData: {
      companyName: string;
      address: string;
      city: string;
      state: string;
      zip: string;
      country: string;
      website: string;
      workNumber: string[];
      location: {
        latitude: number;
        longitude: number;
      };
      timezone: string;
    };
    businessHours: Array<{
      hoursTypeId: string;
      regularHours: Array<{
        openDay: string;
        openTime: Record<string, any>;
        closeDay: string;
        closeTime: Record<string, any>;
      }>;
      specialHours: Array<{
        status: string;
        startDate: {
          year: number;
          month: number;
          day: number;
        };
        startTime: Record<string, any>;
        endDate: {
          year: number;
          month: number;
          day: number;
        };
        endTime: Record<string, any>;
      }>;
    }>;
  };
  accountGroup: {
    accountGroupId: string;
    napData: {
      companyName: string;
      address: string;
      city: string;
      state: string;
      zip: string;
      country: string;
      website: string;
      workNumber: string[];
      timezone: string;
    };
    externalIdentifiers: {
      partnerId: string;
      taxIds: string[];
      vCategoryIds: string[];
      marketId: string;
      salesPersonId: string;
      tags: string[];
      customerIdentifier: string;
      origin: string;
      socialProfileId: string;
      updateOrigin: string;
    };
    socialUrls: Record<string, any>;
    richData: {
      description: string;
      shortDescription: string;
      servicesOffered: string[];
    };
  };
  activationStatus: {
    activationStatuses: Array<{
      businessId: string;
      addonId: string;
    }>;
  };
  seoAddons: {
    addons: Array<{
      businessId: string;
      addonId: string;
    }>;
  };
  seoSettings: {
    localSearchRadius: number;
    favoriteKeywords: string[];
  };
}

export interface ConsolidatedDataResponse {
  consolidatedData: ConsolidatedDataItem[];
}

@Injectable({
  providedIn: 'root',
})
export class ConsolidatedDataService {
  private translate = inject(TranslateService);
  private listingProfileService = inject(ListingProfileApiService);


  async getConsolidatedData(businessIds: string[]): Promise<ConsolidatedDataResponse> {
    try {
      const request = new GetConsolidatedListingProfileDataRequest({
        businessIds: businessIds,
        projectionFilter: new ProjectionFilter({
          externalIdentifiers: true,
          socialUrls: true,
          richData: true,
          napData: true,
          businessHours: true,
          googleAttributes: false,
          googleAttributesMetadata: false,
          bingAttributes: false,
          bingAttributesMetadata: false,
          legacyProductDetails: false,
        }),
        readFilter: {
          includeDeleted: false,
        },
        languageCode: this.translate.currentLang || this.translate.defaultLang || 'en',
      });
      const response = await firstValueFrom(
        this.listingProfileService.getConsolidatedData(request)
      );
      return this.transformApiResponse(response);
    } catch (error) {
      console.error('Failed to get consolidated data:', error);
      
      console.warn('Falling back to mock implementation due to API error');
      return null;
    }
  }


  private transformApiResponse(apiResponse: GetConsolidatedListingProfileDataResponse): ConsolidatedDataResponse {
    if (!apiResponse.consolidatedData) {
      return { consolidatedData: [] };
    }

    const transformedData: ConsolidatedDataItem[] = apiResponse.consolidatedData.map((item: ConsolidatedDataContainer) => {
      return {
        businessId: item.businessId || '',
        listingProfile: {
          businessId: item.listingProfile?.businessId || '',
          created: item.listingProfile?.created?.toISOString() || '',
          updated: item.listingProfile?.updated?.toISOString() || '',
          externalIdentifiers: {
            partnerId: item.listingProfile?.externalIdentifiers?.partnerId || '',
            taxIds: item.listingProfile?.externalIdentifiers?.taxIds || [],
            vCategoryIds: item.listingProfile?.externalIdentifiers?.vCategoryIds || [],
            marketId: item.listingProfile?.externalIdentifiers?.marketId || '',
            salesPersonId: item.listingProfile?.externalIdentifiers?.salesPersonId || '',
            tags: item.listingProfile?.externalIdentifiers?.tags || [],
            customerIdentifier: item.listingProfile?.externalIdentifiers?.customerIdentifier || '',
            origin: item.listingProfile?.externalIdentifiers?.origin || '',
            socialProfileId: item.listingProfile?.externalIdentifiers?.socialProfileId || '',
            updateOrigin: item.listingProfile?.externalIdentifiers?.updateOrigin || '',
          },
          socialUrls: item.listingProfile?.socialUrls || {},
          richData: {
            paymentMethods: item.listingProfile?.richData?.paymentMethods?.map(pm => pm.toString()) || [],
            serviceAvailability: item.listingProfile?.richData?.serviceAvailability || {},
            cellNumber: item.listingProfile?.richData?.cellNumber || '',
            seoKeywords: item.listingProfile?.richData?.seoKeywords || [],
            syncingSeoKeywords: item.listingProfile?.richData?.syncingSeoKeywords || [],
          },
          napData: {
            companyName: item.listingProfile?.napData?.companyName || '',
            address: item.listingProfile?.napData?.address || '',
            city: item.listingProfile?.napData?.city || '',
            state: item.listingProfile?.napData?.state || '',
            zip: item.listingProfile?.napData?.zip || '',
            country: item.listingProfile?.napData?.country || '',
            website: item.listingProfile?.napData?.website || '',
            workNumber: item.listingProfile?.napData?.workNumber || [],
            location: {
              latitude: item.listingProfile?.napData?.location?.latitude || 0,
              longitude: item.listingProfile?.napData?.location?.longitude || 0,
            },
            timezone: item.listingProfile?.napData?.timezone || '',
          },
          businessHours: item.listingProfile?.businessHours?.map(hours => ({
            hoursTypeId: hours.hoursTypeId || '',
            regularHours: hours.regularHours?.map(period => ({
              openDay: period.openDay?.toString() || '',
              openTime: period.openTime || {},
              closeDay: period.closeDay?.toString() || '',
              closeTime: period.closeTime || {},
            })) || [],
            specialHours: hours.specialHours?.map(period => ({
              status: period.status?.toString() || '',
              startDate: {
                year: period.startDate?.year || 0,
                month: period.startDate?.month || 0,
                day: period.startDate?.day || 0,
              },
              startTime: period.startTime || {},
              endDate: {
                year: period.endDate?.year || 0,
                month: period.endDate?.month || 0,
                day: period.endDate?.day || 0,
              },
              endTime: period.endTime || {},
            })) || [],
          })) || [],
        },
        accountGroup: {
          accountGroupId: item.accountGroup?.accountGroupId || '',
          napData: {
            companyName: item.accountGroup?.napData?.companyName || '',
            address: item.accountGroup?.napData?.address || '',
            city: item.accountGroup?.napData?.city || '',
            state: item.accountGroup?.napData?.state || '',
            zip: item.accountGroup?.napData?.zip || '',
            country: item.accountGroup?.napData?.country || '',
            website: item.accountGroup?.napData?.website || '',
            workNumber: item.accountGroup?.napData?.workNumber || [],
            timezone: item.accountGroup?.napData?.timezone || '',
          },
          externalIdentifiers: {
            partnerId: item.accountGroup?.externalIdentifiers?.partnerId || '',
            taxIds: item.accountGroup?.externalIdentifiers?.taxIds || [],
            vCategoryIds: item.accountGroup?.externalIdentifiers?.vCategoryIds || [],
            marketId: item.accountGroup?.externalIdentifiers?.marketId || '',
            salesPersonId: item.accountGroup?.externalIdentifiers?.salesPersonId || '',
            tags: item.accountGroup?.externalIdentifiers?.tags || [],
            customerIdentifier: item.accountGroup?.externalIdentifiers?.customerIdentifier || '',
            origin: item.accountGroup?.externalIdentifiers?.origin || '',
            socialProfileId: item.accountGroup?.externalIdentifiers?.socialProfileId || '',
            updateOrigin: item.accountGroup?.externalIdentifiers?.updateOrigin || '',
          },
          socialUrls: item.accountGroup?.socialUrls || {},
          richData: {
            description: item.accountGroup?.richData?.description || '',
            shortDescription: item.accountGroup?.richData?.shortDescription || '',
            servicesOffered: item.accountGroup?.richData?.servicesOffered || [],
          },
        },
        activationStatus: {
          activationStatuses: item.activationStatus?.activationStatuses?.map(status => ({
            businessId: (status as any).businessId || '',
            addonId: (status as any).addonId || '',
          })) || [],
        },
        seoAddons: {
          addons: item.seoAddons?.addons?.map(addon => ({
            businessId: addon.businessId || '',
            addonId: addon.addonId || '',
          })) || [],
        },
        seoSettings: {
          localSearchRadius: item.seoSettings?.localSearchRadius || 0,
          favoriteKeywords: item.seoSettings?.favoriteKeywords || [],
        },
      };
    });

    return { consolidatedData: transformedData };
  }

  /**
   * Mock implementation as fallback when API fails
   * This simulates the response format expected from the actual API
   */


  /**
   * Extract business keyword data from consolidated response
   * This transforms the consolidated data into the format expected by existing services
   */
  extractBusinessKeywordDataFromConsolidated(
    consolidatedResponse: ConsolidatedDataResponse
  ): Map<string, any> {
    const businessDataMap = new Map<string, any>();

    consolidatedResponse.consolidatedData.forEach((item) => {
      const businessData = {
        businessId: item.businessId,
        businessName: item.accountGroup.napData.companyName || `Business ${item.businessId}`,
        address: this.formatAddress(item.accountGroup.napData),
        existingKeywords: item.listingProfile.richData.seoKeywords?.filter((k) => k !== '') || [],
        favoriteKeywords: item.seoSettings.favoriteKeywords || [],
        syncingKeywords: item.listingProfile.richData.syncingSeoKeywords || [],
        keywordLimit: this.calculateKeywordLimit(item),
        isPro: this.isProEdition(item),
        partnerId: item.accountGroup.externalIdentifiers.partnerId,
        marketId: item.accountGroup.externalIdentifiers.marketId,
        // Include additional data that might be needed
        listingProfile: item.listingProfile,
        accountGroup: item.accountGroup,
        activationStatus: item.activationStatus,
        seoAddons: item.seoAddons,
        seoSettings: item.seoSettings,
      };

      businessDataMap.set(item.businessId, businessData);
    });

    return businessDataMap;
  }


  private formatAddress(napData: any): string {
    const parts = [
      napData.address,
      napData.city,
      napData.state,
      napData.zip,
      napData.country,
    ].filter(Boolean);

    return parts.join(', ');
  }

  private calculateKeywordLimit(item: ConsolidatedDataItem): number {
    // Constants from the existing service
    const FREE_EDITION_KEYWORDS = 3;
    const PAID_EDITION_KEYWORDS = 15;
    const ADDON_INCREMENT = 15;

    // Check if it's a pro edition
    const isPro = this.isProEdition(item);

    // Base limit
    let limit = isPro ? PAID_EDITION_KEYWORDS : FREE_EDITION_KEYWORDS;

    // Add increments for SEO addons
    if (item.seoAddons?.addons) {
      limit += item.seoAddons.addons.length * ADDON_INCREMENT;
    }

    return limit;
  }


  private isProEdition(item: ConsolidatedDataItem): boolean {
    // Constants from the existing service
    const DEMO_PAID_EDITION = 'EDITION-MXWLTQPN';
    const PROD_PAID_EDITION = 'EDITION-CFH5CKHC';

    if (!item.activationStatus?.activationStatuses) {
      return false;
    }

    return item.activationStatus.activationStatuses.some(
      (status) => status.addonId === DEMO_PAID_EDITION || status.addonId === PROD_PAID_EDITION
    );
  }
} 