import { Injectable, inject } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import {
  ListingProfileApiService,
  SEOApiService,
  PartnerSettingsApiService,
  GetMultiListingProfileRequest,
  UpdateListingProfileRequest,
  UpdateOperation,
  RichData,
  FieldMask,
  ProjectionFilter,
  GetPartnerSettingsRequest,
  GetPartnerSettingsResponse,
  SaveSEOSettingsRequest,
  GetActiveSEOAddonsRequest,
} from '@vendasta/listing-products';
import {
  AccountsService,
  AppAndAddonActivationStatus,
  ListAppAndAddonActivationStatusFilter,
} from '@vendasta/accounts/legacy';
import { AccountGroupApiService, GetMultiRequest } from '@vendasta/account-group';
import { ConsolidatedDataService } from './consolidated-data.service';

// Constants from local-seo
const DEMO_PAID_EDITION = 'EDITION-MXWLTQPN';
const PROD_PAID_EDITION = 'EDITION-CFH5CKHC';
const ADDON_INCREMENT = 15;
const FREE_EDITION_KEYWORDS = 3;
const PAID_EDITION_KEYWORDS = 15;

export interface BusinessKeywordData {
  businessId: string;
  businessName: string;
  address: string;
  existingKeywords: string[];
  favoriteKeywords: string[];
  syncingKeywords: string[];
  keywordLimit: number;
  isPro: boolean;
}

export interface BulkKeywordResult {
  businessId: string;
  businessName: string;
  success: boolean;
  addedKeywords: string[];
  skippedKeywords: string[];
  error?: string;
  newKeywordCount: number;
  keywordLimit: number;
}

export interface BulkKeywordRequest {
  businessIds: string[];
  keywords: string[];
  partnerId?: string;
  marketId?: string;
}

@Injectable({
  providedIn: 'root',
})
export class BulkKeywordService {
  private listingProfileService = inject(ListingProfileApiService);
  private seoService = inject(SEOApiService);
  private partnerSettingsService = inject(PartnerSettingsApiService);
  private accountsService = inject(AccountsService);
  private accountGroupService = inject(AccountGroupApiService);
  private translate = inject(TranslateService);
  private snackBar = inject(MatSnackBar);
  private consolidatedDataService = inject(ConsolidatedDataService);

  /**
   * Get existing keyword data for multiple businesses
   * Now uses consolidated data API for improved performance
   */
  async getBusinessKeywordData(businessIds: string[]): Promise<Map<string, BusinessKeywordData>> {
    try {
      // Use consolidated data API instead of multiple separate calls
      const consolidatedResponse = await this.consolidatedDataService.getConsolidatedData(businessIds);
      const businessDataMap = this.consolidatedDataService.extractBusinessKeywordDataFromConsolidated(consolidatedResponse);

      // Convert to BusinessKeywordData format
      const resultMap = new Map<string, BusinessKeywordData>();

      for (const [businessId, businessData] of businessDataMap) {
        const businessKeywordData: BusinessKeywordData = {
          businessId,
          businessName: businessData.businessName,
          address: businessData.address,
          existingKeywords: businessData.existingKeywords,
          favoriteKeywords: businessData.favoriteKeywords,
          syncingKeywords: businessData.syncingKeywords,
          keywordLimit: businessData.keywordLimit,
          isPro: businessData.isPro,
        };

        resultMap.set(businessId, businessKeywordData);
      }

      return resultMap;
    } catch (error) {
      console.error('Failed to get business keyword data:', error);
      throw error;
    }
  }

  /**
   * Add keywords to multiple businesses in bulk
   */
  async addKeywordsToBulkBusinesses(request: BulkKeywordRequest): Promise<BulkKeywordResult[]> {
    const results: BulkKeywordResult[] = [];

    try {
      // Get current business data
      const businessDataMap = await this.getBusinessKeywordData(request.businessIds);

      // Get partner settings for auto-favoriting behavior
      let partnerSettings: GetPartnerSettingsResponse | null = null;
      if (request.partnerId && request.marketId) {
        try {
          partnerSettings = await firstValueFrom(
            this.partnerSettingsService.getPartnerSettings(
              new GetPartnerSettingsRequest({
                partnerId: request.partnerId,
                marketId: request.marketId,
              }),
            ),
          );
        } catch (error) {
          console.warn('Could not get partner settings:', error);
        }
      }

      // Process each business
      const promises = request.businessIds.map(async (businessId) => {
        const businessData = businessDataMap.get(businessId);

        if (!businessData) {
          return {
            businessId,
            businessName: `Business ${businessId}`,
            success: false,
            addedKeywords: [],
            skippedKeywords: request.keywords,
            error: 'Business data not found',
            newKeywordCount: 0,
            keywordLimit: 0,
          };
        }

        return this.addKeywordsToSingleBusiness(businessData, request.keywords, partnerSettings);
      });

      const businessResults = await Promise.all(promises);
      results.push(...businessResults);
    } catch (error) {
      console.error('Bulk keyword addition failed:', error);
      // Create error results for all businesses
      request.businessIds.forEach((businessId) => {
        results.push({
          businessId,
          businessName: `Business ${businessId}`,
          success: false,
          addedKeywords: [],
          skippedKeywords: request.keywords,
          error: 'Bulk operation failed',
          newKeywordCount: 0,
          keywordLimit: 0,
        });
      });
    }

    return results;
  }

  /**
   * Add keywords to a single business
   */
  private async addKeywordsToSingleBusiness(
    businessData: BusinessKeywordData,
    newKeywords: string[],
    partnerSettings: GetPartnerSettingsResponse | null,
  ): Promise<BulkKeywordResult> {
    try {
      // Filter out empty keywords and duplicates
      const cleanKeywords = newKeywords.filter((k) => k && k.trim().length > 0);
      const uniqueNewKeywords = [...new Set(cleanKeywords)];

      // Check which keywords are actually new
      const keywordsToAdd = uniqueNewKeywords.filter((keyword) => !businessData.existingKeywords.includes(keyword));

      if (keywordsToAdd.length === 0) {
        return {
          businessId: businessData.businessId,
          businessName: businessData.businessName,
          success: true,
          addedKeywords: [],
          skippedKeywords: uniqueNewKeywords,
          newKeywordCount: businessData.existingKeywords.length,
          keywordLimit: businessData.keywordLimit,
        };
      }

      // Check keyword limit
      const totalAfterAddition = businessData.existingKeywords.length + keywordsToAdd.length;
      let finalKeywordsToAdd = keywordsToAdd;
      let skippedKeywords: string[] = [];

      if (totalAfterAddition > businessData.keywordLimit) {
        const availableSlots = businessData.keywordLimit - businessData.existingKeywords.length;
        finalKeywordsToAdd = keywordsToAdd.slice(0, availableSlots);
        skippedKeywords = keywordsToAdd.slice(availableSlots);

        if (availableSlots <= 0) {
          return {
            businessId: businessData.businessId,
            businessName: businessData.businessName,
            success: false,
            addedKeywords: [],
            skippedKeywords: keywordsToAdd,
            error: 'Keyword limit reached',
            newKeywordCount: businessData.existingKeywords.length,
            keywordLimit: businessData.keywordLimit,
          };
        }
      }

      // Combine existing and new keywords
      const allKeywords = [...businessData.existingKeywords, ...finalKeywordsToAdd];

      // Update listing profile
      await firstValueFrom(
        this.listingProfileService.update(
          new UpdateListingProfileRequest({
            businessId: businessData.businessId,
            updateOperations: [
              new UpdateOperation({
                richData: new RichData({
                  seoKeywords: allKeywords,
                }),
                fieldMask: new FieldMask({
                  paths: ['seo_keywords'],
                }),
              }),
            ],
          }),
        ),
      );

      // Handle auto-favoriting if enabled
      if (!partnerSettings?.isFavoriteNewKeywordsDisabled && finalKeywordsToAdd.length > 0) {
        try {
          const newFavoriteKeywords = [...new Set([...businessData.favoriteKeywords, ...finalKeywordsToAdd])];

          await firstValueFrom(
            this.seoService.saveSeoSettings(
              new SaveSEOSettingsRequest({
                businessId: businessData.businessId,
                favoriteKeywords: newFavoriteKeywords,
                fieldMask: new FieldMask({
                  paths: ['favorite_keywords'],
                }),
              }),
            ),
          );
        } catch (error) {
          console.warn(`Failed to update favorite keywords for ${businessData.businessId}:`, error);
        }
      }

      // Handle auto-syncing if enabled
      if (!partnerSettings?.isSyncKeywordsDisabled && finalKeywordsToAdd.length > 0) {
        try {
          const newSyncingKeywords = [...new Set([...businessData.syncingKeywords, ...finalKeywordsToAdd])];

          await firstValueFrom(
            this.listingProfileService.update(
              new UpdateListingProfileRequest({
                businessId: businessData.businessId,
                updateOperations: [
                  new UpdateOperation({
                    richData: new RichData({
                      syncingSeoKeywords: newSyncingKeywords,
                    }),
                    fieldMask: new FieldMask({
                      paths: ['syncing_seo_keywords'],
                    }),
                  }),
                ],
              }),
            ),
          );
        } catch (error) {
          console.warn(`Failed to update syncing keywords for ${businessData.businessId}:`, error);
        }
      }

      return {
        businessId: businessData.businessId,
        businessName: businessData.businessName,
        success: true,
        addedKeywords: finalKeywordsToAdd,
        skippedKeywords: skippedKeywords,
        newKeywordCount: businessData.existingKeywords.length + finalKeywordsToAdd.length,
        keywordLimit: businessData.keywordLimit,
      };
    } catch (error) {
      console.error(`Failed to add keywords to business ${businessData.businessId}:`, error);
      return {
        businessId: businessData.businessId,
        businessName: businessData.businessName,
        success: false,
        addedKeywords: [],
        skippedKeywords: newKeywords,
        error: error instanceof Error ? error.message : 'Unknown error',
        newKeywordCount: businessData.existingKeywords.length,
        keywordLimit: businessData.keywordLimit,
      };
    }
  }

  /**
   * Get business edition info and keyword limits
   */
  private async getBusinessEditionInfo(businessId: string): Promise<{ isPro: boolean; keywordLimit: number }> {
    try {
      // Check edition status
      const filters: ListAppAndAddonActivationStatusFilter = {
        appIds: ['MS'],
        statuses: [AppAndAddonActivationStatus.ACTIVATED, AppAndAddonActivationStatus.CANCELED],
      };

      const activations = await firstValueFrom(
        this.accountsService.listAppsAndAddonsActivationStatusesForBusiness(businessId, filters),
      );

      let isPro = false;
      if (activations?.length > 0) {
        const editionId = activations[0].editionId;
        isPro = editionId === DEMO_PAID_EDITION || editionId === PROD_PAID_EDITION;
      }

      // Calculate keyword limit
      let keywordLimit = isPro ? PAID_EDITION_KEYWORDS : FREE_EDITION_KEYWORDS;

      // Check for add-ons
      try {
        const addOnsResponse = await firstValueFrom(
          this.seoService.getActiveSeoAddons(
            new GetActiveSEOAddonsRequest({
              businessId: businessId,
            }),
          ),
        );

        if (addOnsResponse?.activeAddons?.length > 0) {
          const limitIncrement = addOnsResponse.activeAddons[0].count * ADDON_INCREMENT;
          keywordLimit += limitIncrement;
        }
      } catch (error) {
        console.warn(`Could not get add-ons for business ${businessId}:`, error);
      }

      return { isPro, keywordLimit };
    } catch (error) {
      console.warn(`Could not get edition info for business ${businessId}:`, error);
      return { isPro: false, keywordLimit: FREE_EDITION_KEYWORDS };
    }
  }

  /**
   * Format business address for display
   */
  private formatAddress(napData: any): string {
    if (!napData) return '';

    const parts = [];
    if (napData.address) parts.push(napData.address);
    if (napData.city) parts.push(napData.city);
    if (napData.state) parts.push(napData.state);
    if (napData.zip) parts.push(napData.zip);

    return parts.join(', ');
  }

  /**
   * Show success message with detailed results
   */
  showBulkResultMessage(results: BulkKeywordResult[]): void {
    const successful = results.filter((r) => r.success);
    const failed = results.filter((r) => !r.success);
    const totalAdded = successful.reduce((sum, r) => sum + r.addedKeywords.length, 0);

    let message = '';
    if (successful.length > 0 && failed.length === 0) {
      message = this.translate.instant('BRAND_KEYWORD.BULK_SUCCESS.ALL_SUCCESS', {
        totalAdded,
        businessCount: successful.length,
      });
    } else if (successful.length > 0 && failed.length > 0) {
      message = this.translate.instant('BRAND_KEYWORD.BULK_SUCCESS.PARTIAL_SUCCESS', {
        totalAdded,
        successCount: successful.length,
        failedCount: failed.length,
      });
    } else {
      message = this.translate.instant('BRAND_KEYWORD.BULK_SUCCESS.ALL_FAILED', {
        failedCount: failed.length,
      });
    }

    this.snackBar.open(message, '', {
      duration: 6000,
      horizontalPosition: 'center',
      verticalPosition: 'bottom',
      panelClass: successful.length > 0 ? ['success-snackbar'] : ['error-snackbar'],
    });
  }
}
